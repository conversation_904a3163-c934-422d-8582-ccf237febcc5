import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  V<PERSON>tack,
  HStack,
  Grid,
  GridItem,
  Badge,
  Card,
  CardBody,
  CardHeader,
  Heading,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

import { QuestionnairesService } from "@/client"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"

export const Route = createFileRoute("/_layout/questionnaire-history")({
  component: QuestionnaireHistoryPage,
})

// Helper function to get survey type display name
const getSurveyTypeDisplayName = (surveyTypeName: string | null) => {
  if (!surveyTypeName) return "未知評估類型"
  
  switch (surveyTypeName) {
    case "3-6 years questionnaire":
      return "3-6歲"
    case "7-11 years questionnaire":
      return "7-11歲"
    case "12-15 years questionnaire":
      return "12-15歲"
    default:
      return surveyTypeName
  }
}

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "yyyy年MM月dd日", { locale: zhCN })
  } catch {
    return "未知日期"
  }
}

// Helper function to get status badge color and text
const getStatusInfo = (status: string) => {
  switch (status) {
    case "available":
      return { color: "blue", text: "進行中" }
    case "finished":
      return { color: "green", text: "已完成" }
    default:
      return { color: "gray", text: status }
  }
}

function QuestionnaireHistoryPage() {
  const navigate = useNavigate()

  // Get all questionnaires for current client
  const { data: questionnairesData, isLoading, error } = useQuery({
    queryKey: ["my-questionnaires"],
    queryFn: () => QuestionnairesService.getMyQuestionnaires(),
  })

  const questionnaires = questionnairesData?.data || []

  if (isLoading) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter">載入問卷記錄中...</Text>
        </VStack>
      </Box>
    )
  }

  if (error) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter" color="red.500">載入失敗</Text>
          <Button onClick={() => navigate({ to: "/" })}>返回首頁</Button>
        </VStack>
      </Box>
    )
  }

  return (
    <Box bg="#f9f5e9" minH="100vh">
      <Header title="問卷記錄" />
      
      <Box maxW="1200px" mx="auto" px={8} py={8}>
        <VStack gap={6} align="stretch">
          {/* Page Header */}
          <Box textAlign="center">
            <ChineseText
              fontSize="32px"
              fontWeight="bold"
              color="#000000"
              fontFamily="Inter"
              mb={2}
            >
              我的問卷記錄
            </ChineseText>
            <ChineseText
              fontSize="16px"
              color="#666666"
              fontFamily="Inter"
            >
              查看您已完成的智能核心評估記錄
            </ChineseText>
          </Box>

          {/* Questionnaires Grid */}
          {questionnaires.length === 0 ? (
            <Box textAlign="center" py={12}>
              <ChineseText
                fontSize="18px"
                color="#666666"
                fontFamily="Inter"
                mb={4}
              >
                暫無問卷記錄
              </ChineseText>
              <Button
                bg="#d3401f"
                color="#ffffff"
                borderRadius="8px"
                onClick={() => navigate({ to: "/" })}
                _hover={{ bg: "#b8351a" }}
              >
                返回首頁
              </Button>
            </Box>
          ) : (
            <Grid templateColumns="repeat(auto-fit, minmax(350px, 1fr))" gap={6}>
              {questionnaires.map((questionnaire) => {
                const statusInfo = getStatusInfo(questionnaire.status)
                const isFinished = questionnaire.status === "finished"
                
                return (
                  <GridItem key={questionnaire.id}>
                    <Card
                      border="1px solid #e0e0e0"
                      borderRadius="12px"
                      _hover={{ 
                        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                        transform: isFinished ? "translateY(-2px)" : "none"
                      }}
                      transition="all 0.2s"
                      cursor={isFinished ? "pointer" : "default"}
                      onClick={isFinished ? () => navigate({ 
                        to: "/questionnaire-details/$questionnaireId", 
                        params: { questionnaireId: questionnaire.id } 
                      }) : undefined}
                    >
                      <CardHeader pb={2}>
                        <HStack justify="space-between" align="flex-start">
                          <VStack align="flex-start" gap={1} flex={1}>
                            <ChineseText
                              fontSize="18px"
                              fontWeight="bold"
                              color="#000000"
                              fontFamily="Inter"
                            >
                              {questionnaire.name}
                            </ChineseText>
                            <ChineseText
                              fontSize="14px"
                              color="#666666"
                              fontFamily="Inter"
                            >
                              {getSurveyTypeDisplayName(questionnaire.survey_type_name)}智能核心評估
                            </ChineseText>
                          </VStack>
                          <Badge colorScheme={statusInfo.color} fontSize="12px">
                            {statusInfo.text}
                          </Badge>
                        </HStack>
                      </CardHeader>
                      
                      <CardBody pt={0}>
                        <VStack align="flex-start" gap={2}>
                          <HStack>
                            <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                              開始時間：
                            </ChineseText>
                            <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                              {formatDate(questionnaire.created_at)}
                            </ChineseText>
                          </HStack>
                          
                          {questionnaire.finished_at && (
                            <HStack>
                              <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                                完成時間：
                              </ChineseText>
                              <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                                {formatDate(questionnaire.finished_at)}
                              </ChineseText>
                            </HStack>
                          )}

                          {questionnaire.grade_level && (
                            <HStack>
                              <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                                年級：
                              </ChineseText>
                              <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                                {questionnaire.grade_level}
                              </ChineseText>
                            </HStack>
                          )}

                          {isFinished && (
                            <Box pt={2}>
                              <ChineseText
                                fontSize="12px"
                                color="#d3401f"
                                fontFamily="Inter"
                                fontStyle="italic"
                              >
                                點擊查看詳細內容
                              </ChineseText>
                            </Box>
                          )}
                        </VStack>
                      </CardBody>
                    </Card>
                  </GridItem>
                )
              })}
            </Grid>
          )}

          {/* Back Button */}
          <Box textAlign="center" pt={4}>
            <Button
              bg="#2196F3"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => navigate({ to: "/" })}
              _hover={{ bg: "#1976D2" }}
            >
              返回首頁
            </Button>
          </Box>
        </VStack>
      </Box>
    </Box>
  )
}
