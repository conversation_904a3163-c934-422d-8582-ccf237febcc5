import {
  <PERSON>,
  Button,
  Text,
  VStack,
  H<PERSON>tack,
  Stack,
  Flex,
} from "@chakra-ui/react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { useState, useEffect } from "react"
import { z } from "zod"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"
import { QuestionnairesService } from "@/client"

const questionnaireQuestionsSearchSchema = z.object({
  surveyTypeDisplayName: z.string().optional(),
})

export const Route = createFileRoute("/_layout/questionnaire-questions")({
  component: QuestionnaireQuestionsPage,
  validateSearch: (search) => questionnaireQuestionsSearchSchema.parse(search),
})

// Using API response types directly from generated client

const answerOptions = [
  { value: "A", label: "實際情況相比題目描述表現得更出色（或表現出超高的天份）" },
  { value: "B", label: "與實際情況比較，小朋友的情況完全符合題目的描述" },
  { value: "C", label: "與實際情況比較，小朋友的情況大部份符合題目的描述" },
  { value: "D", label: "與實際情況比較，小朋友的情況小部份符合題目的描述" },
  { value: "E", label: "與實際情況比較，小朋友的情況基本不份符合題目的描述" },
  { value: "F", label: "與實際情況比較，小朋友的情況完全不份符合題目的描述" },
]

// Arrow icons as SVG components
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

function QuestionnaireQuestionsPage() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { surveyTypeDisplayName } = Route.useSearch()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<number, string>>({})
  const [selectedAnswer, setSelectedAnswer] = useState<string>("")
  const [isTestMode, setIsTestMode] = useState(false)

  // Get current questionnaire to access questions
  const { data: questionnaire, isLoading: questionnaireLoading } = useQuery({
    queryKey: ["my-questionnaire"],
    queryFn: () => QuestionnairesService.getMyAvailableQuestionnaire(),
  })

  // Get questions from the questionnaire's survey type
  const { data: questionsData, isLoading: questionsLoading } = useQuery({
    queryKey: ["questionnaire-questions", questionnaire?.survey_type_id],
    queryFn: async () => {
      if (!questionnaire?.survey_type_id) return null

      return QuestionnairesService.getPublicSurveyQuestions({ surveyTypeId: questionnaire.survey_type_id })
    },
    enabled: !!questionnaire?.survey_type_id,
  })

  // Transform the API response to match our Question interface
  const questions = questionsData?.data?.map((q: any) => ({
    id: q.question_id,
    text: q.content,
    core_competency: {
      id: 0, // These IDs aren't provided in the response, but we don't need them for display
      name: q.core_competency,
      intelligence_category: {
        id: 0,
        name: q.intelligence_category
      }
    }
  })) || []

  const currentQuestion = questions?.[currentQuestionIndex]
  const totalQuestions = questions?.length || 0

  // Load saved answer for current question
  useEffect(() => {
    const savedAnswer = answers[currentQuestion?.id]
    setSelectedAnswer(savedAnswer || "")
  }, [currentQuestionIndex, answers, currentQuestion?.id])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Test mode activation with Alt+T
      if (event.altKey && event.key === "t") {
        handleTestMode()
        return
      }

      // Quick submit with Alt+S (only in test mode)
      if (isTestMode && event.altKey && event.key === "s") {
        handleSubmitQuestionnaire()
        return
      }

      // Number keys 1-6 for answer selection
      if (event.key >= "1" && event.key <= "6") {
        const answerIndex = parseInt(event.key) - 1
        if (answerIndex < answerOptions.length) {
          handleAnswerSelect(answerOptions[answerIndex].value)
        }
      }
      // Arrow keys for navigation
      else if (event.key === "ArrowLeft" && currentQuestionIndex > 0) {
        handlePrevious()
      }
      else if (event.key === "ArrowRight" && selectedAnswer && currentQuestionIndex < totalQuestions - 1) {
        handleNext()
      }
      // Enter key to submit on last question
      else if (event.key === "Enter" && selectedAnswer && currentQuestionIndex === totalQuestions - 1) {
        handleSubmitQuestionnaire()
      }
    }

    window.addEventListener("keydown", handleKeyPress)
    return () => window.removeEventListener("keydown", handleKeyPress)
  }, [currentQuestionIndex, selectedAnswer, totalQuestions, isTestMode])

  const handleAnswerSelect = (value: string) => {
    setSelectedAnswer(value)
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }))
  }

  // Test mode function to randomly answer all questions
  const handleTestMode = () => {
    if (!questions || questions.length === 0) return

    const randomAnswers: Record<number, string> = {}

    // Generate random answers for all questions
    questions.forEach((question) => {
      const randomIndex = Math.floor(Math.random() * answerOptions.length)
      randomAnswers[question.id] = answerOptions[randomIndex].value
    })

    setAnswers(randomAnswers)
    setIsTestMode(true)

    // Set the current question's answer
    if (currentQuestion) {
      setSelectedAnswer(randomAnswers[currentQuestion.id])
    }

    console.log("Test mode activated - Random answers generated:", randomAnswers)
  }

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    } else {
      // Last question - submit questionnaire
      handleSubmitQuestionnaire()
    }
  }

  // Mutation for submitting questionnaire answers
  const submitAnswersMutation = useMutation({
    mutationFn: async (answersData: Record<number, string>) => {
      // Convert answers to the format expected by the API
      const questionsAndAns = Object.entries(answersData).reduce((acc, [questionId, answer]) => {
        acc[questionId] = answer
        return acc
      }, {} as Record<string, unknown>)

      // Submit answers to API
      await QuestionnairesService.submitQuestionnaireAnswers({
        requestBody: {
          answers: Object.entries(questionsAndAns).map(([questionId, answer]) => ({
            question_id: parseInt(questionId),
            selected_answer: answer as string,
          })),
        }
      })

      // Mark questionnaire as finished
      await QuestionnairesService.finishMyQuestionnaire()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-questionnaire"] })
      navigate({ to: "/questionnaire-results" })
    },
    onError: (error: any) => {
      console.error("Failed to submit questionnaire:", error)
      alert("提交問卷失敗: " + (error?.message || "未知錯誤"))
    },
  })

  const handleSubmitQuestionnaire = () => {
    console.log("Submitting answers:", answers)
    submitAnswersMutation.mutate(answers)
  }

  // Show loading state
  if (questionnaireLoading || questionsLoading || !questions || questions.length === 0) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter">載入問題中...</Text>
          {questionnaireLoading && <Text fontSize="16px" color="gray.600">正在獲取問卷資訊...</Text>}
          {questionsLoading && <Text fontSize="16px" color="gray.600">正在載入問題...</Text>}
        </VStack>
      </Box>
    )
  }

  if (!currentQuestion) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter">問題載入失敗</Text>
          <Button onClick={() => navigate({ to: "/" })}>返回首頁</Button>
        </VStack>
      </Box>
    )
  }

  return (
    <Box bg="#f9f5e9" minH="100vh">
      {/* Header with Background */}
      <Box position="relative">
        {/* Header Background */}
        <Stack w="full" gap={0} position="relative" zIndex={1}>
          <Header title={surveyTypeDisplayName + "智能核心評估"} />

          {/* Progress Bar */}
          <Box
            w="full"
            height="4px"
            bg="rgba(0,0,0,0.1)"
            borderRadius="2px"
          >
            <Box
              width={`${((currentQuestionIndex + 1) / totalQuestions) * 100}%`}
              height="100%"
              bg="#d3401f"
              borderRadius="2px"
              transition="width 0.3s ease"
            />
          </Box>
        </Stack>
      </Box>
      {/* Main Content Container */}
      <Flex
        direction="column"
        align="center"
        justify="flex-start"
        minH="calc(100vh - 148px)"
        px={8}
        py={8}
      >
        <VStack
          gap={8}
          align="stretch"
          w="full"
          flex={1}
        >
          {/* Question Counter and Shortcuts */}
          <HStack justify="space-between" align="center">
            <ChineseText
              fontWeight="bold"
              fontSize="24px"
              color="#000000"
              lineHeight="1.2"
            >
              {currentQuestionIndex + 1}/{totalQuestions} 題
            </ChineseText>

            <VStack align="flex-end" gap={1}>
              <Text fontSize="12px" color="gray.500" fontFamily="Inter">
                快捷鍵: Alt+T 測試模式
              </Text>
              {isTestMode && (
                <Text fontSize="12px" color="gray.500" fontFamily="Inter">
                  Alt+S 快速提交
                </Text>
              )}
            </VStack>
          </HStack>

          {/* Question Text */}
          <ChineseText
            fontFamily="Inter"
            fontWeight="bold"
            fontSize="24px"
            color="#000000"
            lineHeight="1.2"
          >
            {currentQuestion.text}
          </ChineseText>

          {/* Answer Options */}
          <VStack
            gap="5"
            align="stretch"
            flex={1}
            justify="flex-start"
          >
            {answerOptions.map((option) => (
              <HStack
                key={option.value}
                gap="8px"
                align="center"
                cursor="pointer"
                onClick={() => handleAnswerSelect(option.value)}
                p={2}
                borderRadius="8px"
                _hover={{ bg: "rgba(0,0,0,0.05)" }}
                transition="background-color 0.2s ease"
              >
                {/* Radio Button */}
                <Box
                  width="20px"
                  height="20px"
                  borderRadius="50%"
                  border="2px solid #000000"
                  bg={selectedAnswer === option.value ? "#d3401f" : "transparent"}
                  flexShrink={0}
                />

                {/* Answer Text */}
                <ChineseText
                  fontFamily="Inter"
                  fontSize="20px"
                  color="#000000"
                  lineHeight="1.2"
                  flex={1}
                >
                  <ChineseText as="span" fontWeight="bold">
                    {option.value}
                  </ChineseText>
                  <ChineseText as="span" fontWeight="normal">
                    {` ${option.label}`}
                  </ChineseText>
                </ChineseText>
              </HStack>
            ))}
          </VStack>

          {/* Test Mode and Navigation Buttons */}
          <VStack gap={4} align="stretch">
            {/* Test Mode Button */}
            <HStack justify="flex-start">
              <Button
                bg="#4a5568"
                color="#ffffff"
                borderRadius="10px"
                height="40px"
                px="20px"
                fontSize="16px"
                fontWeight="600"
                fontFamily="Inter"
                onClick={handleTestMode}
                disabled={isTestMode}
                _hover={{ bg: "#2d3748" }}
                _disabled={{ opacity: 0.5, cursor: "not-allowed" }}
              >
                <ChineseText>
                  {isTestMode ? "測試模式已啟用" : "啟用測試模式（隨機答題）"}
                </ChineseText>
              </Button>
              {isTestMode && (
                <Text fontSize="14px" color="gray.600" fontFamily="Inter">
                  所有問題已隨機填答，可直接提交
                </Text>
              )}
            </HStack>

            {/* Navigation Buttons */}
            <HStack
              gap="20px"
              justify="flex-start"
            >
              {/* Previous Button */}
              <Button
                bg="#e53e3e"
                color="#ffffff"
                borderRadius="10px"
                height="48px"
                px="24px"
                fontSize="18px"
                fontWeight="600"
                fontFamily="Inter"
                onClick={handlePrevious}
                disabled={currentQuestionIndex === 0}
                _hover={{ bg: "#d32f2f" }}
                _disabled={{ opacity: 0.5, cursor: "not-allowed" }}
              >
                <ArrowLeftIcon />
                <ChineseText ml="8px">上一題</ChineseText>
              </Button>

              {/* Next Button */}
              <Button
                bg="#e53e3e"
                color="#ffffff"
                borderRadius="10px"
                height="48px"
                px="24px"
                fontSize="18px"
                fontWeight="600"
                fontFamily="Inter"
                onClick={handleNext}
                disabled={!selectedAnswer}
                loading={submitAnswersMutation.isPending}
                _hover={{ bg: "#d32f2f" }}
                _disabled={{ opacity: 0.5, cursor: "not-allowed" }}
              >
                <ChineseText mr="8px">
                  {currentQuestionIndex === totalQuestions - 1 ? "完成" : "下一題"}
                </ChineseText>
                {currentQuestionIndex < totalQuestions - 1 && <ArrowRightIcon />}
              </Button>

              {/* Quick Submit Button (only show in test mode) */}
            
            </HStack>
              {isTestMode && (
                <Button
                  bg="#38a169"
                  color="#ffffff"
                  borderRadius="10px"
                  height="48px"
                  px="24px"
                  fontSize="18px"
                  fontWeight="600"
                  fontFamily="Inter"
                  onClick={handleSubmitQuestionnaire}
                  loading={submitAnswersMutation.isPending}
                  _hover={{ bg: "#2f855a" }}
                >
                  <ChineseText>快速提交所有答案</ChineseText>
                </Button>
              )}
          </VStack>

        </VStack>


      </Flex>



    </Box>
  )
}
